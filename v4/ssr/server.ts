import 'zone.js/node';
import 'reflect-metadata';

import { APP_BASE_HREF } from '@angular/common';
import { ngExpressEngine } from '@nguniversal/express-engine';
import * as express from 'express';
import { existsSync, readFileSync } from 'node:fs';
import { join } from 'node:path';
import { AppServerModule } from './src/main.server';
import compression from 'compression';

// The Express app is exported so that it can be used by serverless Functions.
export function app(): express.Express {
  const server = express();
  server.use(compression({ level: 8 }));
  const distFolder = join(process.cwd(), 'dist/browser');
  const indexHtml = existsSync(join(distFolder, 'index.original.html'))
    ? 'index.original.html'
    : 'index.html';

  // Our Universal express-engine (found @ https://github.com/angular/universal/tree/main/modules/express-engine)
  server.engine(
    'html',
    ngExpressEngine({
      inlineCriticalCss: false,
      bootstrap: AppServerModule
    })
  );

  server.set('view engine', 'html');
  server.set('views', distFolder);

  // Example Express Rest API endpoints
  // server.get('/api/**', (req, res) => { });
  // Serve static files from /browser with smart caching
  server.get(
    '*.*',
    (req, res, next) => {
      const filePath = req.path;

      // Files with hashes can be cached longer (they change when content changes)
      const isHashedFile =
        /\.[a-f0-9]{8,}\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i.test(
          filePath
        );

      if (isHashedFile) {
        // Hashed files can be cached for a long time
        res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year
      } else {
        // Non-hashed files (like index.html) should have shorter cache
        res.setHeader('Cache-Control', 'public, max-age=3600'); // 1 hour
      }

      next();
    },
    express.static(distFolder, {
      etag: true,
      lastModified: true
    })
  );

  // All regular routes use the Universal engine
  // server.get('*', (req, res) => {
  //   res.render(indexHtml, { req, providers: [{ provide: APP_BASE_HREF, useValue: req.baseUrl }] });
  // });
  const routesPath = ['/users', '/users/**'];
  server.get(routesPath, (req, res) => {
    // console.log(req);
    res.sendFile(distFolder + '/index.html');
  });

  // Serve the static index.html for the root route to bypass Angular Universal template issues
  server.get('/', (req, res) => {
    const indexPath = join(distFolder, indexHtml);
    console.log(`Serving static index.html from: ${indexPath}`);

    // Read and log the content to see what's actually being served
    const indexContent = readFileSync(indexPath, 'utf8');
    const scriptTags =
      indexContent.match(/<script[^>]*src="[^"]*"[^>]*>/g) || [];
    console.log(`Script tags in deployed index.html:`, scriptTags);

    res.sendFile(indexPath);
  });

  server.get('*', (req, res) => {
    // Read the current index.html file to get the latest script references
    const indexPath = join(distFolder, indexHtml);
    const indexContent = readFileSync(indexPath, 'utf8');

    // Log the current script references for debugging
    const scriptTags =
      indexContent.match(/<script[^>]*src="[^"]*"[^>]*>/g) || [];
    console.log(`Current script tags in ${indexPath}:`, scriptTags);

    res.render(indexHtml, {
      req,
      providers: [{ provide: APP_BASE_HREF, useValue: req.baseUrl }]
    });
  });

  return server;
}

function run(): void {
  const port = process.env['PORT'] || 4000;

  // Start up the Node server
  const server = app();
  server.listen(port, () => {
    console.log(`Node Express server listening on http://localhost:${port}`);
  });
}

// Webpack will replace 'require' with '__webpack_require__'
// '__non_webpack_require__' is a proxy to Node 'require'
// The below code is to ensure that the server is run only when not requiring the bundle.
declare const __non_webpack_require__: NodeRequire;
const mainModule = __non_webpack_require__.main;
const moduleFilename = (mainModule && mainModule.filename) || '';
if (moduleFilename === __filename || moduleFilename.includes('iisnode')) {
  run();
}

export * from './src/main.server';
