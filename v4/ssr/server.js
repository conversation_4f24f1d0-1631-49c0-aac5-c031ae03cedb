import 'zone.js/node';
import 'reflect-metadata';
import { APP_BASE_HREF } from '@angular/common';
import { ngExpressEngine } from '@nguniversal/express-engine';
import express from 'express';
import { existsSync } from 'node:fs';
import { join } from 'node:path';
import { AppServerModule } from './src/app/app.server.module';
import compression from 'compression';
// The Express app is exported so that it can be used by serverless Functions.
export function app() {
  try {
    const server = express();
    server.use(compression({ level: 8 }));
    const distFolder = join(process.cwd(), 'dist/browser');
    console.log(`Looking for dist folder at: ${distFolder}`);
    console.log(`Dist folder exists: ${existsSync(distFolder)}`);
    const indexHtml = existsSync(join(distFolder, 'index.original.html'))
      ? 'index.original.html'
      : 'index.html';
    );
    const scriptTags =
      templateContent.match(/<script[^>]*src="[^"]*"[^>]*>/g) || [];
    console.log(`Template script tags: ${scriptTags.join(',')}`);

    // Our Universal express-engine (found @ https://github.com/angular/universal/tree/main/modules/express-engine)
    server.engine(
      'html',
      ngExpressEngine({
        inlineCriticalCss: false,
        bootstrap: AppServerModule
      })
    );
    server.set('view engine', 'html');
    server.set('views', distFolder);
    // Health check endpoint for Google App Engine
    server.get('/health', (req, res) => {
      res
        .status(200)
        .json({ status: 'ok', timestamp: new Date().toISOString() });
    });
    // Example Express Rest API endpoints
    // server.get('/api/**', (req, res) => { });
    // Serve static files from /browser
    server.get(
      '*.*',
      express.static(distFolder, {
        maxAge: '1y'
      })
    );
    // All regular routes use the Universal engine
    // server.get('*', (req, res) => {
    //   res.render(indexHtml, { req, providers: [{ provide: APP_BASE_HREF, useValue: req.baseUrl }] });
    // });
    const routesPath = ['/users', '/users/**'];
    server.get(routesPath, (req, res) => {
      // console.log(req);
      res.sendFile(distFolder + '/index.html');
    });
    server.get('*', (req, res) => {
      res.render(indexHtml, {
        req,
        providers: [
          { provide: APP_BASE_HREF, useValue: req.baseUrl },
          { provide: 'REQUEST', useValue: req },
          { provide: 'RESPONSE', useValue: res }
        ]
      });
    });
    return server;
  } catch (error) {
    console.error('Error setting up Express app:', error);
    throw error;
  }
}
function run() {
  const port = process.env['PORT'] || 8080;
  try {
    // Start up the Node server
    const server = app();
    server.listen(port, () => {
      console.log(`Node Express server listening on http://localhost:${port}`);
      console.log(`Environment: ${process.env['NODE_ENV']}`);
      console.log(`Server started successfully at ${new Date().toISOString()}`);
    });
    // Handle server errors
    server.on('error', (error) => {
      console.error('Server error:', error);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}
const mainModule = __non_webpack_require__.main;
const moduleFilename = (mainModule && mainModule.filename) || '';
if (moduleFilename === __filename || moduleFilename.includes('iisnode')) {
  run();
}
export * from './src/main.server';
